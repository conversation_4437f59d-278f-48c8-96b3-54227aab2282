# SallamRunUninstall

A beautiful, elegant Python application with Arabic interface support and dark mode for managing installed applications on Windows.

## Features

- 🌙 **Dark Mode Interface** - Modern, elegant dark theme
- 🔤 **Arabic Language Support** - Full RTL (Right-to-Left) text support
- 🚀 **Run Applications** - Launch installed programs
- 🗑️ **Uninstall Applications** - Remove programs from your system
- 📱 **Responsive Design** - Clean, modern UI with smooth interactions
- ℹ️ **About Dialog** - Designer information and contact details
- ✅ **Exit Confirmation** - Elegant exit dialog with thank you message

## Designer Information

**By:** Eng. Hani <PERSON>  
**Mobile:** +967 77298 0 925

## Installation

1. Install Python 3.8 or higher
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

Run the application:
```bash
python main.py
```

## Features Overview

### Main Interface
- **Application List**: Scrollable list of all installed applications
- **Control Panel**: Run and uninstall buttons for selected applications
- **Arabic Text**: All interface elements support Arabic text with proper RTL layout
- **Status Bar**: Real-time status updates

### Dialogs
- **About Dialog**: Shows application information and designer contact details
- **Exit Dialog**: Elegant confirmation dialog when closing the application

### Application Management
- **Run Applications**: Launch any installed program
- **Uninstall Applications**: Remove programs with confirmation dialogs
- **Refresh List**: Update the list of installed applications

## Technical Details

- **Framework**: Python with CustomTkinter for modern UI
- **Arabic Support**: arabic-reshaper and python-bidi for proper text rendering
- **System Integration**: Windows Registry access for application management
- **Threading**: Non-blocking UI with background operations

## Requirements

- Windows Operating System
- Python 3.8+
- See `requirements.txt` for Python dependencies

## File Structure

```
SallamRunUninstall/
├── main.py              # Main application entry point
├── app_manager.py       # Application management functionality
├── ui_components.py     # Custom UI components and styling
├── arabic_support.py    # Arabic text handling utilities
├── requirements.txt     # Python dependencies
└── README.md           # This file
```

## License

Created by Eng. Hani Sallam - All rights reserved.
