"""
Custom UI components and styling for SallamRunUninstall
"""
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from arabic_support import ArabicTextHandler

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class DarkTheme:
    """Dark theme color scheme"""
    BG_PRIMARY = "#1a1a1a"
    BG_SECONDARY = "#2d2d2d"
    BG_TERTIARY = "#3d3d3d"
    TEXT_PRIMARY = "#ffffff"
    TEXT_SECONDARY = "#cccccc"
    ACCENT_BLUE = "#0078d4"
    ACCENT_GREEN = "#107c10"
    ACCENT_RED = "#d13438"
    BORDER = "#404040"
    HOVER = "#404040"

class StyledButton(ctk.CTkButton):
    """Custom styled button with Arabic support"""

    def __init__(self, parent, text="", command=None, **kwargs):
        # Reshape Arabic text if needed
        display_text = ArabicTextHandler.reshape_arabic(text)

        super().__init__(
            parent,
            text=display_text,
            command=command,
            font=("Arial", 12, "bold"),
            corner_radius=8,
            height=40,
            **kwargs
        )

class StyledLabel(ctk.CTkLabel):
    """Custom styled label with Arabic support"""

    def __init__(self, parent, text="", **kwargs):
        # Reshape Arabic text if needed
        display_text = ArabicTextHandler.reshape_arabic(text)

        # Set default font if not provided
        if 'font' not in kwargs:
            kwargs['font'] = ("Arial", 11)

        super().__init__(
            parent,
            text=display_text,
            **kwargs
        )

class StyledFrame(ctk.CTkFrame):
    """Custom styled frame"""

    def __init__(self, parent, **kwargs):
        super().__init__(
            parent,
            corner_radius=10,
            fg_color=DarkTheme.BG_SECONDARY,
            **kwargs
        )

class AboutDialog:
    """About dialog window"""

    def __init__(self, parent):
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("About - حول")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        self.create_widgets()

    def create_widgets(self):
        """Create about dialog widgets"""
        # Main frame
        main_frame = StyledFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Logo placeholder (you can add actual logo later)
        logo_label = StyledLabel(main_frame, text="🚀", font=("Arial", 48))
        logo_label.pack(pady=(20, 10))

        # App name
        app_name = StyledLabel(main_frame, text="SallamRunUninstall",
                              font=("Arial", 16, "bold"))
        app_name.pack(pady=5)

        # Designer info
        designer_label = StyledLabel(main_frame, text="By: Eng. Hani Sallam",
                                   font=("Arial", 12))
        designer_label.pack(pady=5)

        # Mobile number
        mobile_label = StyledLabel(main_frame, text="Mobile:",
                                 font=("Arial", 11))
        mobile_label.pack(pady=2)

        number_label = StyledLabel(main_frame, text="+967 77298 0 925",
                                 font=("Arial", 11, "bold"))
        number_label.pack(pady=2)

        # OK button
        ok_button = StyledButton(main_frame, text="OK",
                               command=self.dialog.destroy)
        ok_button.pack(pady=20)

class ExitDialog:
    """Exit confirmation dialog"""

    def __init__(self, parent, on_confirm):
        self.on_confirm = on_confirm
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Exit - خروج")
        self.dialog.geometry("350x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))

        self.create_widgets()

    def create_widgets(self):
        """Create exit dialog widgets"""
        # Main frame
        main_frame = StyledFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Logo
        logo_label = StyledLabel(main_frame, text="🚀", font=("Arial", 32))
        logo_label.pack(pady=(20, 10))

        # App name
        app_name = StyledLabel(main_frame, text="SallamRunUninstall",
                              font=("Arial", 14, "bold"))
        app_name.pack(pady=5)

        # Thank you message
        arabic_texts = ArabicTextHandler.get_arabic_texts()
        thank_you_text = ArabicTextHandler.reshape_arabic(arabic_texts['thank_you'])
        thank_you_label = StyledLabel(main_frame, text=thank_you_text,
                                    font=("Arial", 12))
        thank_you_label.pack(pady=10)

        # Exit button
        exit_button = StyledButton(main_frame, text="Exit - خروج",
                                 command=self.confirm_exit,
                                 fg_color=DarkTheme.ACCENT_RED)
        exit_button.pack(pady=10)

    def confirm_exit(self):
        """Confirm and exit"""
        self.dialog.destroy()
        self.on_confirm()
