"""
Custom UI components and styling for SallamRunUninstall
"""
import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from arabic_support import ArabicTextHandler

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class DarkTheme:
    """Dark theme color scheme"""
    BG_PRIMARY = "#1a1a1a"
    BG_SECONDARY = "#2d2d2d"
    BG_TERTIARY = "#3d3d3d"
    TEXT_PRIMARY = "#ffffff"
    TEXT_SECONDARY = "#cccccc"
    ACCENT_BLUE = "#0078d4"
    ACCENT_GREEN = "#107c10"
    ACCENT_RED = "#d13438"
    BORDER = "#404040"
    HOVER = "#404040"

class StyledButton(ctk.CTkButton):
    """Custom styled button with Arabic support and RTL alignment"""

    def __init__(self, parent, text="", command=None, **kwargs):
        # Reshape Arabic text if needed
        display_text = ArabicTextHandler.reshape_arabic(text)

        # Set default font if not provided - better Arabic support
        if 'font' not in kwargs:
            kwargs['font'] = ("Segoe UI", 12, "bold")

        # Set RTL alignment for Arabic text
        if ArabicTextHandler.is_arabic_text(text):
            if 'anchor' not in kwargs:
                kwargs['anchor'] = "center"  # Center for buttons looks better

        super().__init__(
            parent,
            text=display_text,
            command=command,
            corner_radius=8,
            height=40,
            **kwargs
        )

class StyledLabel(ctk.CTkLabel):
    """Custom styled label with Arabic support and RTL alignment"""

    def __init__(self, parent, text="", **kwargs):
        # Reshape Arabic text if needed
        display_text = ArabicTextHandler.reshape_arabic(text)

        # Set default font if not provided - use fonts that support Arabic better
        if 'font' not in kwargs:
            kwargs['font'] = ("Segoe UI", 11)  # Better Arabic support than Arial

        # Set RTL alignment for Arabic text
        if ArabicTextHandler.is_arabic_text(text):
            if 'anchor' not in kwargs:
                kwargs['anchor'] = "e"  # Right align for Arabic

        super().__init__(
            parent,
            text=display_text,
            **kwargs
        )

class StyledFrame(ctk.CTkFrame):
    """Custom styled frame"""

    def __init__(self, parent, **kwargs):
        super().__init__(
            parent,
            corner_radius=10,
            fg_color=DarkTheme.BG_SECONDARY,
            **kwargs
        )

class AboutDialog:
    """About dialog window"""

    def __init__(self, parent):
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("About - حول")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 50,
            parent.winfo_rooty() + 50
        ))

        self.create_widgets()

    def create_widgets(self):
        """Create about dialog widgets"""
        # Main frame
        main_frame = StyledFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Logo placeholder (you can add actual logo later)
        logo_label = StyledLabel(main_frame, text="🚀", font=("Arial", 48))
        logo_label.pack(pady=(20, 10))

        # App name
        app_name = StyledLabel(main_frame, text="SallamRunUninstall",
                              font=("Arial", 16, "bold"))
        app_name.pack(pady=5)

        # Designer info
        designer_label = StyledLabel(main_frame, text="By: Eng. Hani Sallam",
                                   font=("Arial", 12))
        designer_label.pack(pady=5)

        # Mobile number
        mobile_label = StyledLabel(main_frame, text="Mobile:",
                                 font=("Arial", 11))
        mobile_label.pack(pady=2)

        number_label = StyledLabel(main_frame, text="+967 77298 0 925",
                                 font=("Arial", 11, "bold"))
        number_label.pack(pady=2)

        # OK button
        ok_button = StyledButton(main_frame, text="OK",
                               command=self.dialog.destroy)
        ok_button.pack(pady=20)

class ExitConfirmDialog:
    """Exit confirmation dialog asking if user wants to exit - Full screen"""

    def __init__(self, parent, on_confirm):
        self.on_confirm = on_confirm
        self.parent = parent
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Confirm Exit - تأكيد الخروج")

        # Make the dialog full screen / maximized
        self.dialog.state('zoomed')  # Maximize on Windows
        # Alternative: self.dialog.attributes('-fullscreen', True)  # True fullscreen

        self.dialog.resizable(True, True)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Set background color to match dark theme
        self.dialog.configure(fg_color=DarkTheme.BG_PRIMARY)

        self.create_widgets()

    def create_widgets(self):
        """Create confirmation dialog widgets for full screen display"""
        # Configure grid for centering
        self.dialog.grid_rowconfigure(0, weight=1)
        self.dialog.grid_columnconfigure(0, weight=1)

        # Main container frame - centered in full screen
        container_frame = StyledFrame(self.dialog)
        container_frame.grid(row=0, column=0, sticky="", padx=100, pady=100)

        # Content frame with larger size for full screen
        content_frame = StyledFrame(container_frame)
        content_frame.pack(padx=60, pady=60)

        # Large logo for full screen
        logo_label = StyledLabel(content_frame, text="🚀", font=("Segoe UI", 72))
        logo_label.pack(pady=(40, 20))

        # App name - larger for full screen
        app_name = StyledLabel(content_frame, text="SallamRunUninstall",
                              font=("Segoe UI", 28, "bold"))
        app_name.pack(pady=15)

        # Confirmation message in Arabic - larger text
        arabic_texts = ArabicTextHandler.get_arabic_texts()
        confirm_label = StyledLabel(content_frame, text=arabic_texts['confirm_exit'],
                                  font=("Segoe UI", 20),
                                  anchor="center")
        confirm_label.pack(pady=30)

        # Button frame with larger spacing
        button_frame = ctk.CTkFrame(content_frame, fg_color="transparent")
        button_frame.pack(pady=40)

        # Larger Yes and No buttons for full screen
        yes_btn = StyledButton(button_frame, text=arabic_texts['yes'],
                             command=self.show_thank_you,
                             fg_color=DarkTheme.ACCENT_GREEN,
                             width=150,
                             height=50,
                             font=("Segoe UI", 16, "bold"))
        yes_btn.pack(side="left", padx=30)

        no_btn = StyledButton(button_frame, text=arabic_texts['no'],
                            command=self.dialog.destroy,
                            fg_color=DarkTheme.ACCENT_RED,
                            width=150,
                            height=50,
                            font=("Segoe UI", 16, "bold"))
        no_btn.pack(side="right", padx=30)

    def show_thank_you(self):
        """Show thank you dialog and then exit"""
        self.dialog.destroy()
        ExitThankYouDialog(self.parent, self.on_confirm)

class ExitThankYouDialog:
    """Thank you dialog shown before exiting"""

    def __init__(self, parent, on_confirm):
        self.on_confirm = on_confirm
        self.parent = parent
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title("Thank You - شكراً")
        self.dialog.geometry("400x250")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            parent.winfo_rootx() + 100,
            parent.winfo_rooty() + 100
        ))

        self.create_widgets()

    def create_widgets(self):
        """Create thank you dialog widgets"""
        # Main frame
        main_frame = StyledFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Logo
        logo_label = StyledLabel(main_frame, text="🚀", font=("Segoe UI", 32))
        logo_label.pack(pady=(20, 10))

        # App name
        app_name = StyledLabel(main_frame, text="SallamRunUninstall",
                              font=("Segoe UI", 14, "bold"))
        app_name.pack(pady=5)

        # Thank you message in Arabic
        arabic_texts = ArabicTextHandler.get_arabic_texts()
        thank_you_label = StyledLabel(main_frame, text=arabic_texts['thank_you'],
                                    font=("Segoe UI", 14),
                                    anchor="center")
        thank_you_label.pack(pady=15)

        # OK button
        ok_btn = StyledButton(main_frame, text=arabic_texts['ok'],
                            command=self.confirm_exit,
                            fg_color=DarkTheme.ACCENT_BLUE,
                            width=100)
        ok_btn.pack(pady=15)

    def confirm_exit(self):
        """Exit the application"""
        self.dialog.destroy()
        self.parent.quit()
        self.parent.destroy()
