"""
Arabic text support and RTL layout utilities for SallamRunUninstall
"""
import arabic_reshaper
from bidi.algorithm import get_display

class ArabicTextHandler:
    """Handle Arabic text reshaping and bidirectional text display"""
    
    @staticmethod
    def reshape_arabic(text):
        """Reshape Arabic text for proper display"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception:
            return text
    
    @staticmethod
    def get_arabic_texts():
        """Return Arabic text translations for the application"""
        return {
            'app_title': 'سلام تشغيل وإلغاء التثبيت',
            'run_app': 'تشغيل التطبيق',
            'uninstall_app': 'إلغاء تثبيت التطبيق',
            'about': 'حول',
            'exit': 'خروج',
            'refresh': 'تحديث',
            'select_app': 'اختر تطبيقاً',
            'no_app_selected': 'لم يتم اختيار تطبيق',
            'uninstall_confirm': 'هل أنت متأكد من إلغاء تثبيت هذا التطبيق؟',
            'uninstall_success': 'تم إلغاء التثبيت بنجاح',
            'uninstall_failed': 'فشل في إلغاء التثبيت',
            'run_success': 'تم تشغيل التطبيق',
            'run_failed': 'فشل في تشغيل التطبيق',
            'thank_you': 'شكراً لاستخدام هذا التطبيق',
            'confirm_exit': 'هل تريد الخروج من التطبيق؟',
            'yes': 'نعم',
            'no': 'لا',
            'ok': 'موافق',
            'cancel': 'إلغاء',
            'loading': 'جاري التحميل...',
            'installed_apps': 'التطبيقات المثبتة'
        }
