"""
SallamRunUninstall - Main Application
A beautiful Arabic-interface application for running and uninstalling programs
By: Eng. <PERSON><PERSON>
"""
import tkinter as tk
from tkinter import ttk
import customtkinter as ctk
import threading
from arabic_support import ArabicTextHandler
from ui_components import (
    DarkTheme, StyledButton, StyledLabel, StyledFrame,
    AboutDialog, ExitConfirmDialog
)
from app_manager import ApplicationManager

class SallamRunUninstallApp:
    """Main application class"""

    def __init__(self):
        self.app_manager = ApplicationManager()
        self.arabic_texts = ArabicTextHandler.get_arabic_texts()
        self.installed_apps = []
        self.selected_app = None

        # Create main window
        self.root = ctk.CTk()
        self.setup_main_window()
        self.create_widgets()
        self.load_applications()

    def setup_main_window(self):
        """Setup main window properties"""
        self.root.title("SallamRunUninstall - سلام تشغيل وإلغاء التثبيت")
        self.root.geometry("800x600")
        self.root.minsize(700, 500)

        # Set window icon (you can add an actual icon file later)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        # Configure grid weights
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # Handle window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_widgets(self):
        """Create all UI widgets"""
        # Header frame
        self.create_header()

        # Main content frame
        self.create_main_content()

        # Footer frame
        self.create_footer()

    def create_header(self):
        """Create header with logo and title with proper Arabic RTL support"""
        header_frame = StyledFrame(self.root)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=(10, 5))
        header_frame.grid_columnconfigure(1, weight=1)

        # Logo (placeholder - you can replace with actual logo)
        logo_label = StyledLabel(header_frame, text="🚀", font=("Segoe UI", 32))
        logo_label.grid(row=0, column=0, padx=20, pady=10)

        # Title and subtitle with RTL support
        title_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        title_frame.grid(row=0, column=1, sticky="ew", padx=10)
        title_frame.grid_columnconfigure(0, weight=1)

        # Main title
        title_label = StyledLabel(title_frame, text="SallamRunUninstall",
                                font=("Segoe UI", 20, "bold"))
        title_label.grid(row=0, column=0, sticky="w", pady=2)

        # Arabic subtitle with proper RTL alignment
        subtitle_label = StyledLabel(title_frame, text=self.arabic_texts['app_title'],
                                   font=("Segoe UI", 14),
                                   anchor="w")
        subtitle_label.grid(row=1, column=0, sticky="w", pady=2)

        # Header buttons with Arabic text
        button_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        button_frame.grid(row=0, column=2, padx=20)

        about_btn = StyledButton(button_frame, text=self.arabic_texts['about'],
                               command=self.show_about, width=100)
        about_btn.pack(side="top", padx=5, pady=2)

        refresh_btn = StyledButton(button_frame, text=self.arabic_texts['refresh'],
                                 command=self.refresh_applications, width=100)
        refresh_btn.pack(side="top", padx=5, pady=2)

    def create_main_content(self):
        """Create main content area"""
        content_frame = StyledFrame(self.root)
        content_frame.grid(row=1, column=0, sticky="nsew", padx=10, pady=5)
        content_frame.grid_columnconfigure(0, weight=2)
        content_frame.grid_columnconfigure(1, weight=1)
        content_frame.grid_rowconfigure(0, weight=1)

        # Applications list frame
        self.create_apps_list(content_frame)

        # Control panel frame
        self.create_control_panel(content_frame)

    def create_apps_list(self, parent):
        """Create applications list"""
        list_frame = StyledFrame(parent)
        list_frame.grid(row=0, column=0, sticky="nsew", padx=(0, 5))
        list_frame.grid_columnconfigure(0, weight=1)
        list_frame.grid_rowconfigure(1, weight=1)

        # List title
        list_title = StyledLabel(list_frame,
                               text=self.arabic_texts['installed_apps'],
                               font=("Arial", 14, "bold"))
        list_title.grid(row=0, column=0, pady=10)

        # Scrollable frame for apps
        self.apps_scrollable = ctk.CTkScrollableFrame(list_frame)
        self.apps_scrollable.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        self.apps_scrollable.grid_columnconfigure(0, weight=1)

    def create_control_panel(self, parent):
        """Create control panel with action buttons and proper Arabic alignment"""
        control_frame = StyledFrame(parent)
        control_frame.grid(row=0, column=1, sticky="nsew", padx=(5, 0))

        # Selected app info with Arabic RTL support
        info_label = StyledLabel(control_frame, text=self.arabic_texts['select_app'],
                               font=("Segoe UI", 12, "bold"),
                               anchor="center")
        info_label.pack(pady=20)

        self.selected_app_label = StyledLabel(control_frame, text="",
                                            font=("Segoe UI", 10),
                                            wraplength=200,
                                            anchor="center")
        self.selected_app_label.pack(pady=10)

        # Action buttons with proper Arabic text
        self.run_btn = StyledButton(control_frame,
                                  text=self.arabic_texts['run_app'],
                                  command=self.run_selected_app,
                                  fg_color=DarkTheme.ACCENT_GREEN,
                                  state="disabled",
                                  font=("Segoe UI", 12, "bold"))
        self.run_btn.pack(pady=10, padx=20, fill="x")

        self.uninstall_btn = StyledButton(control_frame,
                                        text=self.arabic_texts['uninstall_app'],
                                        command=self.uninstall_selected_app,
                                        fg_color=DarkTheme.ACCENT_RED,
                                        state="disabled",
                                        font=("Segoe UI", 12, "bold"))
        self.uninstall_btn.pack(pady=10, padx=20, fill="x")

    def create_footer(self):
        """Create footer with status and exit button with Arabic support"""
        footer_frame = StyledFrame(self.root)
        footer_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(5, 10))
        footer_frame.grid_columnconfigure(0, weight=1)

        # Status label
        self.status_label = StyledLabel(footer_frame, text="Ready",
                                      font=("Segoe UI", 10))
        self.status_label.grid(row=0, column=0, sticky="w", padx=20, pady=10)

        # Exit button with Arabic text
        exit_btn = StyledButton(footer_frame, text=self.arabic_texts['exit'],
                              command=self.on_closing, width=120,
                              font=("Segoe UI", 12, "bold"))
        exit_btn.grid(row=0, column=1, padx=20, pady=10)

    def load_applications(self):
        """Load installed applications in a separate thread"""
        self.status_label.configure(text=self.arabic_texts['loading'])

        def load_apps():
            self.installed_apps = self.app_manager.get_installed_applications()
            self.root.after(0, self.display_applications)

        threading.Thread(target=load_apps, daemon=True).start()

    def display_applications(self):
        """Display applications in the scrollable list"""
        # Clear existing widgets
        for widget in self.apps_scrollable.winfo_children():
            widget.destroy()

        # Add application buttons
        for i, app in enumerate(self.installed_apps):
            app_btn = ctk.CTkButton(
                self.apps_scrollable,
                text=app['name'][:50] + "..." if len(app['name']) > 50 else app['name'],
                command=lambda a=app: self.select_application(a),
                anchor="w",
                height=35,
                font=("Arial", 10)
            )
            app_btn.grid(row=i, column=0, sticky="ew", padx=5, pady=2)

        self.status_label.configure(text=f"Loaded {len(self.installed_apps)} applications")

    def select_application(self, app):
        """Select an application"""
        self.selected_app = app
        self.selected_app_label.configure(text=app['name'])

        # Enable action buttons
        self.run_btn.configure(state="normal")
        self.uninstall_btn.configure(state="normal")

    def run_selected_app(self):
        """Run the selected application"""
        if not self.selected_app:
            return

        self.status_label.configure(text="Running application...")

        def run_app():
            success = self.app_manager.run_application(self.selected_app)
            self.root.after(0, lambda: self.handle_run_result(success))

        threading.Thread(target=run_app, daemon=True).start()

    def handle_run_result(self, success):
        """Handle run application result"""
        if success:
            self.app_manager.show_success_message('run_success')
            self.status_label.configure(text="Application started successfully")
        else:
            self.app_manager.show_error_message('run_failed')
            self.status_label.configure(text="Failed to start application")

    def uninstall_selected_app(self):
        """Uninstall the selected application"""
        if not self.selected_app:
            return

        # Confirm uninstall
        if not self.app_manager.show_confirmation_dialog('uninstall_confirm'):
            return

        self.status_label.configure(text="Uninstalling application...")

        def uninstall_app():
            success = self.app_manager.uninstall_application(self.selected_app)
            self.root.after(0, lambda: self.handle_uninstall_result(success))

        threading.Thread(target=uninstall_app, daemon=True).start()

    def handle_uninstall_result(self, success):
        """Handle uninstall result"""
        if success:
            self.app_manager.show_success_message('uninstall_success')
            self.status_label.configure(text="Application uninstalled successfully")
            self.refresh_applications()
        else:
            self.app_manager.show_error_message('uninstall_failed')
            self.status_label.configure(text="Failed to uninstall application")

    def refresh_applications(self):
        """Refresh the applications list"""
        self.selected_app = None
        self.selected_app_label.configure(text="")
        self.run_btn.configure(state="disabled")
        self.uninstall_btn.configure(state="disabled")
        self.load_applications()

    def show_about(self):
        """Show about dialog"""
        AboutDialog(self.root)

    def on_closing(self):
        """Handle window closing with proper exit confirmation dialog"""
        ExitConfirmDialog(self.root, lambda: None)  # ExitConfirmDialog handles the exit flow

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SallamRunUninstallApp()
    app.run()
