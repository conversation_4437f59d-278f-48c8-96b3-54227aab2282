"""
Application management functionality for SallamRunUninstall
"""
import os
import subprocess
import psutil
import winreg
from tkinter import messagebox
from arabic_support import ArabicTextHandler

class ApplicationManager:
    """Manage installed applications - run and uninstall"""
    
    def __init__(self):
        self.arabic_texts = ArabicTextHandler.get_arabic_texts()
    
    def get_installed_applications(self):
        """Get list of installed applications from Windows registry"""
        apps = []
        
        # Registry paths to check for installed programs
        registry_paths = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall")
        ]
        
        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.Enum<PERSON>ey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    try:
                                        install_location = winreg.QueryValueEx(subkey, "InstallLocation")[0]
                                    except FileNotFoundError:
                                        install_location = ""
                                    
                                    try:
                                        uninstall_string = winreg.QueryValueEx(subkey, "UninstallString")[0]
                                    except FileNotFoundError:
                                        uninstall_string = ""
                                    
                                    # Skip system components and updates
                                    if not self._is_system_component(display_name):
                                        apps.append({
                                            'name': display_name,
                                            'install_location': install_location,
                                            'uninstall_string': uninstall_string,
                                            'registry_key': subkey_name
                                        })
                                except FileNotFoundError:
                                    continue
                        except Exception:
                            continue
            except Exception:
                continue
        
        # Remove duplicates and sort
        unique_apps = {}
        for app in apps:
            if app['name'] not in unique_apps:
                unique_apps[app['name']] = app
        
        return sorted(unique_apps.values(), key=lambda x: x['name'].lower())
    
    def _is_system_component(self, name):
        """Check if the application is a system component that shouldn't be shown"""
        system_keywords = [
            'Microsoft Visual C++',
            'Microsoft .NET',
            'Windows',
            'Security Update',
            'Hotfix',
            'Service Pack',
            'Update for',
            'KB'
        ]
        
        name_lower = name.lower()
        return any(keyword.lower() in name_lower for keyword in system_keywords)
    
    def run_application(self, app_info):
        """Run the selected application"""
        try:
            install_location = app_info.get('install_location', '')
            
            if install_location and os.path.exists(install_location):
                # Try to find executable in install location
                exe_files = []
                for root, dirs, files in os.walk(install_location):
                    for file in files:
                        if file.endswith('.exe') and not file.lower().startswith('unins'):
                            exe_files.append(os.path.join(root, file))
                
                if exe_files:
                    # Run the first executable found
                    subprocess.Popen([exe_files[0]], shell=True)
                    return True
            
            # Alternative: try to run by name (for apps in PATH)
            try:
                subprocess.Popen([app_info['name']], shell=True)
                return True
            except:
                pass
            
            return False
            
        except Exception as e:
            print(f"Error running application: {e}")
            return False
    
    def uninstall_application(self, app_info):
        """Uninstall the selected application"""
        try:
            uninstall_string = app_info.get('uninstall_string', '')
            
            if uninstall_string:
                # Clean up the uninstall string
                if uninstall_string.startswith('"') and uninstall_string.endswith('"'):
                    uninstall_string = uninstall_string[1:-1]
                
                # Add silent uninstall flags if possible
                if 'msiexec' in uninstall_string.lower():
                    if '/I{' in uninstall_string:
                        uninstall_string = uninstall_string.replace('/I{', '/X{')
                    uninstall_string += ' /quiet /norestart'
                
                # Execute uninstall command
                result = subprocess.run(uninstall_string, shell=True, capture_output=True)
                return result.returncode == 0
            
            return False
            
        except Exception as e:
            print(f"Error uninstalling application: {e}")
            return False
    
    def show_success_message(self, message_key):
        """Show success message in Arabic"""
        message = ArabicTextHandler.reshape_arabic(self.arabic_texts[message_key])
        messagebox.showinfo("Success - نجح", message)
    
    def show_error_message(self, message_key):
        """Show error message in Arabic"""
        message = ArabicTextHandler.reshape_arabic(self.arabic_texts[message_key])
        messagebox.showerror("Error - خطأ", message)
    
    def show_confirmation_dialog(self, message_key):
        """Show confirmation dialog in Arabic"""
        message = ArabicTextHandler.reshape_arabic(self.arabic_texts[message_key])
        yes_text = ArabicTextHandler.reshape_arabic(self.arabic_texts['yes'])
        no_text = ArabicTextHandler.reshape_arabic(self.arabic_texts['no'])
        
        result = messagebox.askyesno("Confirm - تأكيد", message)
        return result
